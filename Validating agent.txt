Of course. This is a fantastic evolution of the concept. Making the LLM "state-aware" by informing it of your current position is the critical step to transform it from a generic analyst into a personalized decision-support tool.

By providing your current holding, you change the LLM's task from:
"Is this a good signal to take from cash?"
to the much more powerful and relevant question:
"Given the position I'm already in, what is the correct action to take in response to this new signal?"

This allows the LLM to reason about closing, holding, or rolling a position, which is exactly what a real trader does.

Here is the final, complete Python script for validation_agent.py. It includes all the necessary logic to read your position from a text file and incorporate it into a more sophisticated prompt for the LLM.

How to Prepare

Create the Position File: In the same directory as the script, create a simple text file named my_position.txt.

If you are holding an option, the file should look exactly like this (with your own values):

code
Code
download
content_copy
expand_less

type: CALL
strike: 450.50
expiry: 2025-09-19

If you are in cash (no position), either make the file empty or simply delete it. The script will automatically assume you are in cash if the file doesn't exist or is empty.

Get Your API Key: Make sure you have your free API key from OpenRouter.ai and have set it as an environment variable.

Final Script: validation_agent.py
code
Python
download
content_copy
expand_less
IGNORE_WHEN_COPYING_START
IGNORE_WHEN_COPYING_END
# validation_agent.py
# A two-agent system for validating RL trading signals with an LLM expert.
# This version is STATE-AWARE: it reads your current position from 'my_position.txt'.

import os
import json
import requests
import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta

# --- Sperandeo Feature Engineering Functions (from Trader Vic.txt) ---
# This logic quantifies classic technical analysis principles into numerical features.

def identify_trend(df: pd.DataFrame) -> pd.DataFrame:
    """Identifies the current trend and quantifies it numerically."""
    df['is_peak'] = (df['High'] > df['High'].shift(1)) & (df['High'] > df['High'].shift(-1))
    df['is_trough'] = (df['Low'] < df['Low'].shift(1)) & (df['Low'] < df['Low'].shift(-1))
    peaks = df[df['is_peak']]['High']
    troughs = df[df['is_trough']]['Low']
    df['last_peak'] = peaks.ffill()
    df['prev_peak'] = peaks.shift(1).ffill()
    df['last_trough'] = troughs.ffill()
    df['prev_trough'] = troughs.shift(1).ffill()
    is_uptrend = (df['last_peak'] > df['prev_peak']) & (df['last_trough'] > df['prev_trough'])
    is_downtrend = (df['last_peak'] < df['prev_peak']) & (df['last_trough'] < df['prev_trough'])
    df['trend_state'] = 0
    df.loc[is_uptrend, 'trend_state'] = 1
    df.loc[is_downtrend, 'trend_state'] = -1
    df.drop(columns=['is_peak', 'is_trough', 'last_peak', 'prev_peak', 'last_trough', 'prev_trough'], inplace=True)
    return df

def apply_123_rule(df: pd.DataFrame) -> pd.DataFrame:
    """Applies Sperandeo's 1-2-3 Rule and quantifies the state of the reversal pattern."""
    df['is_peak'] = (df['High'] > df['High'].shift(1)) & (df['High'] > df['High'].shift(-1))
    df['is_trough'] = (df['Low'] < df['Low'].shift(1)) & (df['Low'] < df['Low'].shift(-1))
    df['peak_val'] = np.where(df['is_peak'], df['High'], np.nan)
    df['trough_val'] = np.where(df['is_trough'], df['Low'], np.nan)
    df['peak_val'].ffill(inplace=True)
    df['trough_val'].ffill(inplace=True)
    df['uptrend_reversal_point'] = df.loc[df['is_trough'].shift().fillna(False), 'trough_val'].ffill()
    df['downtrend_reversal_point'] = df.loc[df['is_peak'].shift().fillna(False), 'peak_val'].ffill()
    lower_high_formed = (df['is_peak']) & (df['peak_val'] < df['peak_val'].shift(1))
    df['lower_high_armed'] = lower_high_formed.replace(False, np.nan).ffill().fillna(False)
    break_below_low = df['Close'] < df['uptrend_reversal_point']
    uptrend_reversal_triggered = df['lower_high_armed'] & break_below_low
    higher_low_formed = (df['is_trough']) & (df['trough_val'] > df['trough_val'].shift(1))
    df['higher_low_armed'] = higher_low_formed.replace(False, np.nan).ffill().fillna(False)
    break_above_high = df['Close'] > df['downtrend_reversal_point']
    downtrend_reversal_triggered = df['higher_low_armed'] & break_above_high
    df['123_reversal_state'] = 0.0
    df.loc[df['lower_high_armed'], '123_reversal_state'] = 0.5
    df.loc[df['higher_low_armed'], '123_reversal_state'] = -0.5
    df.loc[uptrend_reversal_triggered, '123_reversal_state'] = 1.0
    df.loc[downtrend_reversal_triggered, '123_reversal_state'] = -1.0
    df.drop(columns=['is_peak', 'is_trough', 'peak_val', 'trough_val', 'uptrend_reversal_point', 
                     'downtrend_reversal_point', 'lower_high_armed', 'higher_low_armed'], inplace=True)
    return df

def apply_2b_rule(df: pd.DataFrame, lookback: int = 5) -> pd.DataFrame:
    """Applies Sperandeo's 2B Rule and quantifies the signal numerically."""
    df['is_peak'] = (df['High'] > df['High'].shift(1)) & (df['High'] > df['High'].shift(-1))
    df['is_trough'] = (df['Low'] < df['Low'].shift(1)) & (df['Low'] < df['Low'].shift(-1))
    df['last_peak_high'] = df[df['is_peak']]['High'].ffill()
    df['last_trough_low'] = df[df['is_trough']]['Low'].ffill()
    breakout = df['High'] > df['last_peak_high'].shift(1)
    breakout_in_lookback = breakout.rolling(window=lookback, min_periods=1).sum() > 0
    failure = df['Close'] < df['last_peak_high'].shift(1)
    top_signal = breakout_in_lookback & failure
    breakdown = df['Low'] < df['last_trough_low'].shift(1)
    breakdown_in_lookback = breakdown.rolling(window=lookback, min_periods=1).sum() > 0
    recovery = df['Close'] > df['last_trough_low'].shift(1)
    bottom_signal = breakdown_in_lookback & recovery
    df['2b_signal'] = 0
    df.loc[top_signal & (top_signal != top_signal.shift(1).fillna(False)), '2b_signal'] = 1
    df.loc[bottom_signal & (bottom_signal != bottom_signal.shift(1).fillna(False)), '2b_signal'] = -1
    df.drop(columns=['is_peak', 'is_trough', 'last_peak_high', 'last_trough_low'], inplace=True)
    return df

def quantify_consecutive_days(df: pd.DataFrame) -> pd.DataFrame:
    """Quantifies trend exhaustion based on the spirit of the Four Day Rule."""
    price_change = df['Close'].diff()
    change_sign = np.sign(price_change).fillna(0)
    streaks = change_sign.groupby((change_sign != change_sign.shift()).cumsum()).cumsum()
    df['consecutive_days'] = streaks
    return df

def generate_sperandeo_features(df: pd.DataFrame) -> pd.DataFrame:
    """A master function to apply all Sperandeo rules."""
    df_features = df.copy()
    df_features = identify_trend(df_features)
    df_features = apply_123_rule(df_features)
    df_features = apply_2b_rule(df_features)
    df_features = quantify_consecutive_days(df_features)
    return df_features

# --- Main Agent Logic ---

def read_current_position(file_path: str = 'my_position.txt') -> dict or None:
    """
    Reads the trader's current position from a simple text file.
    Returns a dictionary with position details or None if in cash.
    """
    if not os.path.exists(file_path):
        print("INFO: 'my_position.txt' not found. Assuming you are in cash.")
        return None

    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        if not lines:
            print("INFO: 'my_position.txt' is empty. Assuming you are in cash.")
            return None

        position = {}
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                position[key.strip().lower()] = value.strip()
        
        # Validate the position details
        if not all(k in position for k in ['type', 'strike', 'expiry']):
            raise ValueError("Position file is malformed. Must contain 'type', 'strike', and 'expiry'.")
        
        position['type'] = position['type'].upper()
        if position['type'] not in ['CALL', 'PUT']:
            raise ValueError("Position 'type' must be either 'CALL' or 'PUT'.")
            
        position['strike'] = float(position['strike'])
        # You could add date validation for expiry if needed
        
        return position
    except Exception as e:
        print(f"ERROR: Could not read or parse 'my_position.txt'. Error: {e}")
        print("Please fix the file or delete it to assume a cash position.")
        # We will exit here to prevent making a decision with bad data.
        exit()


def get_market_data(ticker: str, days: int) -> pd.DataFrame:
    """Fetches the last N days of market data for the given ticker."""
    print(f"Fetching last {days} days of data for {ticker}...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days * 1.5) # Fetch more to ensure we get enough trading days
    data = yf.download(ticker, start=start_date.strftime('%Y-%m-%d'), end=end_date.strftime('%Y-%m-%d'), progress=False)
    if data.empty:
        raise ValueError(f"Failed to fetch data for {ticker}")
    print("Data fetched successfully.")
    return data.tail(days)

def construct_prompt(rl_signal: dict, sperandeo_features: pd.DataFrame, current_position: dict or None) -> str:
    """Constructs the detailed, state-aware prompt for the LLM."""
    
    latest_features = sperandeo_features.iloc[-1]
    
    position_str = "Currently in cash (no open position)."
    if current_position:
        position_str = (
            f"Currently HOLDING a {current_position['type']} option with:\n"
            f"- Strike Price: {current_position['strike']}\n"
            f"- Expiration Date: {current_position['expiry']}"
        )

    prompt = f"""
    You are an expert trading analyst specializing in the methods of Victor Sperandeo ("Trader Vic"). Your task is to provide a final, actionable recommendation by validating a signal from a Reinforcement Learning (RL) agent, considering the trader's CURRENT OPEN POSITION.

    **1. Current Trader Position:**
    {position_str}

    **2. Primary Signal from RL Agent:**
    - **Decision:** {rl_signal.get('decision_type', 'N/A')}
    - **Details:** {json.dumps(rl_signal.get('details')) if rl_signal.get('details') else 'N/A'}
    - **RL Model Confidence:** {rl_signal.get('confidence_score', 'N/A')}

    **3. Sperandeo Quantitative Features (Latest Data):**
    - **Trend State:** {latest_features['trend_state']:.1f} (1: Uptrend, -1: Downtrend, 0: Sideways)
    - **1-2-3 Reversal State:** {latest_features['123_reversal_state']:.1f} (1: Top Triggered, 0.5: Top Armed, -1: Bottom Triggered, -0.5: Bottom Armed)
    - **2B Signal:** {latest_features['2b_signal']:.1f} (1: 2B Top/Failed Breakout, -1: 2B Bottom/Failed Breakdown)
    - **Consecutive Days:** {latest_features['consecutive_days']:.1f} (Positive: Up-day streak, Negative: Down-day streak. Magnitudes > 4 are significant.)

    **Your Task:**
    1.  **Analyze the Sperandeo features.** Briefly state what each feature indicates about the current market state.
    2.  **Formulate Your Market Bias.** Based *only* on the features, state your own independent market bias: "Bullish", "Bearish", or "Neutral".
    3.  **Compare and Reason (Context is Key).** Compare your bias with the RL agent's signal *in the context of the current position*.
        - If the RL signal aligns with the current position (e.g., holding a CALL, signal is HOLD), explain if the features support continuing to hold.
        - If the RL signal conflicts (e.g., holding a CALL, signal is BUY_PUT), explain if the features justify closing the old position and reversing.
        - If the RL signal suggests a new trade from cash, but you are holding a position, evaluate if it's better to close the current position to take the new one.
    4.  **Provide a Final Agreement Score.** On a scale of 1.0 to 10.0 (in 0.5 intervals), quantify your agreement with the *action implied by the RL signal, given the current position*. A score of 10.0 means you strongly agree with closing a current position to follow the new signal, or continuing to hold if the signal is HOLD. A score of 1.0 means you strongly disagree.

    **Output Format:**
    You MUST provide your response in a valid JSON format, like this:
    {{
      "market_bias": "...",
      "reasoning": "...",
      "agreement_score": X.X
    }}
    """
    return prompt

def get_llm_analysis(rl_signal: dict, sperandeo_features: pd.DataFrame, current_position: dict or None) -> dict:
    """Calls the OpenRouter API to get the LLM's state-aware analysis."""
    
    LLM_MODEL = "mistralai/mistral-7b-instruct" 
    OPENROUTER_API_KEY = os.environ.get("OPENROUTER_API_KEY")

    if not OPENROUTER_API_KEY:
        raise ValueError("OPENROUTER_API_KEY environment variable not set.")

    print(f"Contacting LLM ({LLM_MODEL}) for validation...")
    
    prompt = construct_prompt(rl_signal, sperandeo_features, current_position)

    try:
        response = requests.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {OPENROUTER_API_KEY}",
                "Content-Type": "application/json"
            },
            data=json.dumps({
                "model": LLM_MODEL,
                "messages": [{"role": "user", "content": prompt}]
            })
        )
        response.raise_for_status()

        result = response.json()
        llm_output_str = result['choices'][0]['message']['content']
        
        print("LLM response received. Parsing JSON...")
        if llm_output_str.startswith("```json"):
            llm_output_str = llm_output_str[7:-4]

        llm_analysis = json.loads(llm_output_str)
        
        if not all(k in llm_analysis for k in ["market_bias", "reasoning", "agreement_score"]):
            raise ValueError("LLM response is missing required keys.")
            
        print("LLM analysis parsed successfully.")
        return llm_analysis

    except requests.exceptions.RequestException as e:
        print(f"ERROR: API request failed: {e}")
        return None
    except (json.JSONDecodeError, KeyError, ValueError) as e:
        print(f"ERROR: Failed to parse LLM response: {e}")
        print(f"Raw LLM Output:\n---\n{llm_output_str}\n---")
        return None

def main():
    """Main execution function."""
    try:
        # --- Step 0: Read the Trader's Current Position ---
        current_position = read_current_position()
        print("--- 0. Current Trader Position ---")
        if current_position:
            print(json.dumps(current_position, indent=2))
        else:
            print("In Cash")

        # --- Step 1: Get the RL Agent's Signal ---
        signal_file = 'trading_signal_v3.1.json'
        if not os.path.exists(signal_file):
            print(f"\nERROR: Signal file '{signal_file}' not found.")
            print("Please run 'python SPY.py --signal' first to generate a signal.")
            return

        with open(signal_file, 'r') as f:
            rl_signal = json.load(f)

        print("\n--- 1. RL Agent Signal ---")
        print(json.dumps(rl_signal, indent=2))
        
        # --- Step 2: Get Market Data & Generate Sperandeo Features ---
        spy_data = get_market_data("SPY", days=100)
        sperandeo_features = generate_sperandeo_features(spy_data)
        
        print("\n--- 2. Sperandeo Feature Analysis (Latest Day) ---")
        print(sperandeo_features.tail(1))

        # --- Step 3: Get LLM Validation ---
        llm_analysis = get_llm_analysis(rl_signal, sperandeo_features, current_position)

        if not llm_analysis:
            print("\nCould not get a valid analysis from the LLM. Aborting.")
            return

        print("\n--- 3. LLM Validation Agent Analysis ---")
        print(json.dumps(llm_analysis, indent=2))

        # --- Step 4: Apply Final Decision Logic ---
        print("\n--- 4. Final Trade Decision ---")
        
        agreement_score = llm_analysis.get("agreement_score", 0.0)
        rl_decision = rl_signal.get("decision_type", "ERROR")
        llm_bias = llm_analysis.get("market_bias", "Unknown").lower()

        is_conflict = False
        if "CALL" in rl_decision and "bearish" in llm_bias:
            is_conflict = True
        if "PUT" in rl_decision and "bullish" in llm_bias:
            is_conflict = True
        if rl_decision == "HOLD" and "neutral" not in llm_bias:
            is_conflict = True

        print(f"Analysis: RL Signal='{rl_decision}', LLM Bias='{llm_bias.title()}', Conflict={is_conflict}, Score={agreement_score}")

        if is_conflict and agreement_score < 8.0:
            print("\nFINAL DECISION: DO NOT EXECUTE. Reason: Conflict between agents and agreement score is below the threshold of 8.0.")
        elif agreement_score >= 8.0:
            print(f"\nFINAL DECISION: EXECUTE. Reason: Agreement score of {agreement_score} is high, validating the RL signal.")
        else:
            print(f"\nFINAL DECISION: DO NOT EXECUTE. Reason: Agreement score of {agreement_score} is below the execution threshold of 8.0.")

    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}")

if __name__ == "__main__":
    main()